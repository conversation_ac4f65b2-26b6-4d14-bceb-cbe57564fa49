^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\SERIAL\CMAKELISTS.TXT
setlocal
F:\Reference\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Users/<USER>/Documents/augment-projects/serial -BC:/Users/<USER>/Documents/augment-projects/serial/build --check-stamp-file C:/Users/<USER>/Documents/augment-projects/serial/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
