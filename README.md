# C++ 串口工具

一个简单易用的跨平台C++串口通信工具，支持Windows和Linux系统。

## 功能特性

- 🔍 自动扫描可用串口（包括虚拟串口）
- ⚙️ 支持多种波特率配置
- 📤 支持文本数据发送
- 📥 支持数据接收
- 🖥️ 友好的命令行界面
- 🔄 跨平台支持（Windows/Linux）
- ⚡ 轻量级，无外部依赖

## 编译要求

- C++17 或更高版本
- CMake 3.10 或更高版本
- Windows: Visual Studio 2017+ 或 MinGW
- Linux: GCC 7+ 或 Clang 5+

## 编译方法

### Windows (使用 Visual Studio)
```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### Windows (使用 MinGW)
```bash
mkdir build
cd build
cmake -G "MinGW Makefiles" ..
mingw32-make
```

### Linux
```bash
mkdir build
cd build
cmake ..
make
```

## 使用方法

运行编译后的可执行文件：
```bash
# Windows
./bin/SerialTool.exe

# Linux
./bin/SerialTool
```

## 命令说明

### 基本命令
- `help, h` - 显示帮助信息
- `list, ls` - 列出可用串口
- `exit, quit, q` - 退出程序
- `clear, cls` - 清屏

### 串口操作
- `open <port> [baudrate] [databits] [stopbits] [parity]` - 打开串口
- `close` - 关闭串口
- `status` - 显示串口状态

### 数据传输
- `send <data>` 或 `s <data>` - 发送文本数据
- `sendhex <hex>` 或 `sh <hex>` - 发送十六进制数据
- `read` 或 `r` - 读取数据
- `monitor` 或 `m` - 开始监控模式

## 使用示例

### 1. 列出可用串口
```
SerialTool> list
Available ports:
  COM20
  COM21
```

### 2. 打开串口
```
SerialTool> open COM20 115200
Opened COM20 at 115200 baud
```

### 3. 发送数据
```
SerialTool> send Hello World
Sent: "Hello World"
```

### 4. 读取数据
```
SerialTool> read
Received: "Hello"
```

### 5. 测试虚拟串口
如果您有虚拟串口软件（如com0com），可以创建一对虚拟串口进行测试：
- 在一个终端打开COM20
- 在另一个终端打开COM21
- 在一个端口发送数据，在另一个端口接收

## 支持的串口参数

### 波特率
- 9600, 19200, 38400, 57600
- 115200 (默认)

### 其他参数
- 数据位: 8
- 停止位: 1
- 校验位: 无
- 流控制: 无

## 项目结构

```
serial/
├── CMakeLists.txt          # CMake 配置文件
├── README.md              # 说明文档
└── src/
    ├── main.cpp           # 主程序
    ├── SerialPort.h       # 串口类头文件
    └── SerialPort.cpp     # 串口类实现
```

## 注意事项

1. **权限问题**: 在Linux系统中，可能需要将用户添加到 `dialout` 组才能访问串口设备：
   ```bash
   sudo usermod -a -G dialout $USER
   ```

2. **串口占用**: 确保串口没有被其他程序占用

3. **设备路径**: 
   - Windows: COM1, COM2, COM3...
   - Linux: /dev/ttyUSB0, /dev/ttyACM0, /dev/ttyS0...

## 故障排除

### 串口打开失败
- 检查串口是否存在
- 确认串口没有被其他程序占用
- 检查用户权限（Linux）

### 数据发送/接收失败
- 确认串口参数设置正确
- 检查硬件连接
- 验证对端设备配置

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
