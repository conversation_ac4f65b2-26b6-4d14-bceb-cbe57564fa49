﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{EBFB6489-8F0A-31A1-ABD5-DF9B74B654D0}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\augment-projects\serial\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Documents/augment-projects/serial/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
F:\Reference\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Users/<USER>/Documents/augment-projects/serial -BC:/Users/<USER>/Documents/augment-projects/serial/build --check-stamp-file C:/Users/<USER>/Documents/augment-projects/serial/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeCCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeSystem.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Documents/augment-projects/serial/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
F:\Reference\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Users/<USER>/Documents/augment-projects/serial -BC:/Users/<USER>/Documents/augment-projects/serial/build --check-stamp-file C:/Users/<USER>/Documents/augment-projects/serial/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeCCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeSystem.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Documents/augment-projects/serial/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
F:\Reference\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Users/<USER>/Documents/augment-projects/serial -BC:/Users/<USER>/Documents/augment-projects/serial/build --check-stamp-file C:/Users/<USER>/Documents/augment-projects/serial/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeCCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeSystem.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Documents/augment-projects/serial/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
F:\Reference\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Users/<USER>/Documents/augment-projects/serial -BC:/Users/<USER>/Documents/augment-projects/serial/build --check-stamp-file C:/Users/<USER>/Documents/augment-projects/serial/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeCCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\3.30.5\CMakeSystem.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake;F:\Reference\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Documents\augment-projects\serial\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Documents\augment-projects\serial\build\ZERO_CHECK.vcxproj">
      <Project>{2D71321A-8CDD-3B5C-B09B-1A8D004079BD}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Documents\augment-projects\serial\build\SerialTool.vcxproj">
      <Project>{C154129C-B8CA-35EF-9969-CFB337E807CD}</Project>
      <Name>SerialTool</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>