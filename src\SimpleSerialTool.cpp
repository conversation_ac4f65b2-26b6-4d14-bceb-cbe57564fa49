#include <iostream>
#include <string>
#include <vector>
#include <sstream>
//#include <iomanip>

#ifdef _WIN32
#include <windows.h>
#include <conio.h>
#else
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <dirent.h>
#endif

class SimpleSerialTool {
private:
#ifdef _WIN32
    HANDLE handle;
#else
    int fd;
    struct termios oldTermios;
#endif
    bool isOpen;
    std::string portName;

public:
    SimpleSerialTool() : isOpen(false) {
#ifdef _WIN32
        handle = INVALID_HANDLE_VALUE;
#else
        fd = -1;
#endif
    }

    ~SimpleSerialTool() {
        close();
    }

    std::vector<std::string> listPorts() {
        std::vector<std::string> ports;

#ifdef _WIN32
        // Enhanced Windows port enumeration - check more COM ports
        for (int i = 1; i <= 256; ++i) {
            std::string portName = "COM" + std::to_string(i);

            // Try to query the port using QueryDosDevice first
            char targetPath[5000];
            DWORD result = QueryDosDeviceA(portName.c_str(), targetPath, 5000);
            if (result != 0) {
                // Port exists in system, now try to open it
                HANDLE h = CreateFileA(
                    ("\\\\.\\"+portName).c_str(),  // Use full device path
                    GENERIC_READ | GENERIC_WRITE,
                    0, nullptr, OPEN_EXISTING,
                    FILE_ATTRIBUTE_NORMAL, nullptr
                );

                if (h != INVALID_HANDLE_VALUE) {
                    ports.push_back(portName);
                    CloseHandle(h);
                } else {
                    // Even if we can't open it, it might be in use by another app
                    DWORD error = GetLastError();
                    if (error == ERROR_ACCESS_DENIED || error == ERROR_SHARING_VIOLATION) {
                        ports.push_back(portName + " (in use)");
                    }
                }
            }
        }

        // If no ports found with QueryDosDevice, try registry method
        if (ports.empty()) {
            // Fallback: try to open ports directly
            for (int i = 1; i <= 20; ++i) {
                std::string portName = "COM" + std::to_string(i);
                HANDLE h = CreateFileA(
                    ("\\\\.\\"+portName).c_str(),
                    GENERIC_READ | GENERIC_WRITE,
                    0, nullptr, OPEN_EXISTING, 0, nullptr
                );

                if (h != INVALID_HANDLE_VALUE) {
                    ports.push_back(portName);
                    CloseHandle(h);
                }
            }
        }
#else
        // Linux port enumeration
        DIR* dir = opendir("/dev");
        if (dir) {
            struct dirent* entry;
            while ((entry = readdir(dir)) != nullptr) {
                std::string name = entry->d_name;
                if (name.find("ttyUSB") == 0 || name.find("ttyACM") == 0 ||
                    name.find("ttyS") == 0) {
                    ports.push_back("/dev/" + name);
                }
            }
            closedir(dir);
        }
#endif
        return ports;
    }

    bool open(const std::string& port, int baudRate = 115200) {
        if (isOpen) close();

        portName = port;

#ifdef _WIN32
        std::string fullPortName = "\\\\.\\" + port;
        handle = CreateFileA(
            fullPortName.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0, nullptr, OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL, nullptr
        );

        if (handle == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            std::cout << "Error opening port: ";
            switch (error) {
                case ERROR_FILE_NOT_FOUND:
                    std::cout << "Port not found" << std::endl;
                    break;
                case ERROR_ACCESS_DENIED:
                    std::cout << "Access denied (port may be in use)" << std::endl;
                    break;
                case ERROR_SHARING_VIOLATION:
                    std::cout << "Port is already in use by another application" << std::endl;
                    break;
                default:
                    std::cout << "Error code " << error << std::endl;
                    break;
            }
            return false;
        }
        
        DCB dcb = {};
        dcb.DCBlength = sizeof(dcb);
        
        if (!GetCommState(handle, &dcb)) {
            CloseHandle(handle);
            handle = INVALID_HANDLE_VALUE;
            return false;
        }
        
        dcb.BaudRate = baudRate;
        dcb.ByteSize = 8;
        dcb.StopBits = ONESTOPBIT;
        dcb.Parity = NOPARITY;
        dcb.fBinary = TRUE;
        dcb.fOutxCtsFlow = FALSE;
        dcb.fOutxDsrFlow = FALSE;
        dcb.fDtrControl = DTR_CONTROL_DISABLE;
        dcb.fRtsControl = RTS_CONTROL_DISABLE;
        dcb.fOutX = FALSE;
        dcb.fInX = FALSE;
        
        if (!SetCommState(handle, &dcb)) {
            CloseHandle(handle);
            handle = INVALID_HANDLE_VALUE;
            return false;
        }
        
        COMMTIMEOUTS timeouts = {};
        timeouts.ReadIntervalTimeout = 50;
        timeouts.ReadTotalTimeoutConstant = 1000;
        timeouts.ReadTotalTimeoutMultiplier = 10;
        timeouts.WriteTotalTimeoutConstant = 1000;
        timeouts.WriteTotalTimeoutMultiplier = 10;
        
        if (!SetCommTimeouts(handle, &timeouts)) {
            CloseHandle(handle);
            handle = INVALID_HANDLE_VALUE;
            return false;
        }
#else
        fd = ::open(port.c_str(), O_RDWR | O_NOCTTY | O_NDELAY);
        if (fd == -1) return false;
        
        if (tcgetattr(fd, &oldTermios) != 0) {
            ::close(fd);
            fd = -1;
            return false;
        }
        
        struct termios tty = {};
        if (tcgetattr(fd, &tty) != 0) {
            ::close(fd);
            fd = -1;
            return false;
        }
        
        speed_t speed = B115200;
        switch (baudRate) {
            case 9600: speed = B9600; break;
            case 19200: speed = B19200; break;
            case 38400: speed = B38400; break;
            case 57600: speed = B57600; break;
            case 115200: speed = B115200; break;
            default: speed = B115200; break;
        }
        
        cfsetospeed(&tty, speed);
        cfsetispeed(&tty, speed);
        
        tty.c_cflag &= ~CSIZE;
        tty.c_cflag |= CS8;
        tty.c_cflag &= ~CSTOPB;
        tty.c_cflag &= ~PARENB;
        tty.c_cflag |= CREAD | CLOCAL;
        tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
        tty.c_iflag &= ~(IXON | IXOFF | IXANY);
        tty.c_oflag &= ~OPOST;
        
        tty.c_cc[VMIN] = 0;
        tty.c_cc[VTIME] = 10;
        
        if (tcsetattr(fd, TCSANOW, &tty) != 0) {
            ::close(fd);
            fd = -1;
            return false;
        }
#endif
        
        isOpen = true;
        return true;
    }

    void close() {
        if (!isOpen) return;
        
#ifdef _WIN32
        if (handle != INVALID_HANDLE_VALUE) {
            CloseHandle(handle);
            handle = INVALID_HANDLE_VALUE;
        }
#else
        if (fd != -1) {
            tcsetattr(fd, TCSANOW, &oldTermios);
            ::close(fd);
            fd = -1;
        }
#endif
        isOpen = false;
    }

    bool write(const std::string& data) {
        if (!isOpen) return false;
        
#ifdef _WIN32
        DWORD bytesWritten;
        return WriteFile(handle, data.c_str(), data.length(), &bytesWritten, nullptr) &&
               bytesWritten == data.length();
#else
        ssize_t result = ::write(fd, data.c_str(), data.length());
        return result == static_cast<ssize_t>(data.length());
#endif
    }

    std::string read(size_t maxBytes = 1024) {
        if (!isOpen) return "";
        
        std::vector<char> buffer(maxBytes);
        
#ifdef _WIN32
        DWORD bytesRead;
        if (ReadFile(handle, buffer.data(), maxBytes, &bytesRead, nullptr)) {
            return std::string(buffer.data(), bytesRead);
        }
#else
        ssize_t bytesRead = ::read(fd, buffer.data(), maxBytes);
        if (bytesRead > 0) {
            return std::string(buffer.data(), bytesRead);
        }
#endif
        return "";
    }

    void run() {
        std::cout << "=== Simple C++ Serial Tool ===" << std::endl;
        std::cout << "Type 'help' for commands" << std::endl << std::endl;
        
        std::string command;
        while (true) {
            std::cout << "SerialTool> ";
            std::getline(std::cin, command);
            
            if (command.empty()) continue;
            
            std::istringstream iss(command);
            std::string cmd;
            iss >> cmd;
            
            if (cmd == "help" || cmd == "h") {
                showHelp();
            }
            else if (cmd == "list" || cmd == "ls") {
                auto ports = listPorts();
                std::cout << "Available ports:" << std::endl;
                for (const auto& port : ports) {
                    std::cout << "  " << port << std::endl;
                }
                if (ports.empty()) {
                    std::cout << "  No ports found" << std::endl;
                }
            }
            else if (cmd == "open") {
                std::string port;
                int baud = 115200;
                iss >> port;
                if (iss >> baud) {
                    // Baud rate provided
                }
                if (port.empty()) {
                    std::cout << "Usage: open <port> [baudrate]" << std::endl;
                } else {
                    if (open(port, baud)) {
                        std::cout << "Opened " << port << " at " << baud << " baud" << std::endl;
                    } else {
                        std::cout << "Failed to open " << port << std::endl;
                    }
                }
            }
            else if (cmd == "close") {
                close();
                std::cout << "Port closed" << std::endl;
            }
            else if (cmd == "send" || cmd == "s") {
                std::string data;
                std::getline(iss, data);
                if (!data.empty() && data[0] == ' ') data = data.substr(1);
                if (write(data)) {
                    std::cout << "Sent: \"" << data << "\"" << std::endl;
                } else {
                    std::cout << "Send failed" << std::endl;
                }
            }
            else if (cmd == "read" || cmd == "r") {
                std::string data = read();
                if (!data.empty()) {
                    std::cout << "Received: \"" << data << "\"" << std::endl;
                } else {
                    std::cout << "No data" << std::endl;
                }
            }
            else if (cmd == "exit" || cmd == "quit" || cmd == "q") {
                break;
            }
            else {
                std::cout << "Unknown command: " << cmd << std::endl;
            }
        }
    }

private:
    void showHelp() {
        std::cout << "Available commands:" << std::endl;
        std::cout << "  help, h          - Show this help" << std::endl;
        std::cout << "  list, ls         - List available ports" << std::endl;
        std::cout << "  open <port> [baud] - Open serial port" << std::endl;
        std::cout << "  close            - Close port" << std::endl;
        std::cout << "  send <data>      - Send data" << std::endl;
        std::cout << "  read, r          - Read data" << std::endl;
        std::cout << "  exit, quit, q    - Exit program" << std::endl;
        std::cout << std::endl;
        std::cout << "Examples:" << std::endl;
        std::cout << "  open COM3 115200" << std::endl;
        std::cout << "  send Hello World" << std::endl;
    }
};

int main() {
    try {
        SimpleSerialTool tool;
        tool.run();
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    return 0;
}
