#include "SerialPort.h"
#include <iostream>
#include <algorithm>
#include <thread>
#include <chrono>

#ifdef _WIN32
// Windows headers already included in SerialPort.h
#else
#include <dirent.h>
#include <cstring>
#endif

SerialPort::SerialPort() 
#ifdef _WIN32
    : handle_(INVALID_HANDLE_VALUE), isOpen_(false)
#else
    : fd_(-1), isOpen_(false)
#endif
{
}

SerialPort::~SerialPort() {
    close();
}

std::vector<std::string> SerialPort::getAvailablePorts() {
    std::vector<std::string> ports;
    
#ifdef _WIN32
    // Windows implementation
    for (int i = 1; i <= 256; ++i) {
        std::string portName = "COM" + std::to_string(i);
        HANDLE handle = CreateFileA(
            portName.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            0,
            nullptr
        );

        if (handle != INVALID_HANDLE_VALUE) {
            ports.push_back(portName);
            CloseHandle(handle);
        }
    }
#else
    // Linux implementation
    DIR* dir = opendir("/dev");
    if (dir) {
        struct dirent* entry;
        while ((entry = readdir(dir)) != nullptr) {
            std::string name = entry->d_name;
            if (name.find("ttyUSB") == 0 || name.find("ttyACM") == 0 || 
                name.find("ttyS") == 0 || name.find("tty.") == 0) {
                ports.push_back("/dev/" + name);
            }
        }
        closedir(dir);
    }
#endif
    
    std::sort(ports.begin(), ports.end());
    return ports;
}

bool SerialPort::open(const std::string& portName, const SerialConfig& config) {
    if (isOpen_) {
        close();
    }
    
    portName_ = portName;
    config_ = config;
    
#ifdef _WIN32
    handle_ = CreateFileA(
        portName.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        nullptr
    );
    
    if (handle_ == INVALID_HANDLE_VALUE) {
        return false;
    }
#else
    fd_ = ::open(portName.c_str(), O_RDWR | O_NOCTTY | O_NDELAY);
    if (fd_ == -1) {
        return false;
    }

    // Save original terminal settings
    if (tcgetattr(fd_, &oldTermios_) != 0) {
        ::close(fd_);
        fd_ = -1;
        return false;
    }
#endif
    
    if (!configurePort()) {
        close();
        return false;
    }
    
    isOpen_ = true;
    return true;
}

void SerialPort::close() {
    if (!isOpen_) return;
    
#ifdef _WIN32
    if (handle_ != INVALID_HANDLE_VALUE) {
        CloseHandle(handle_);
        handle_ = INVALID_HANDLE_VALUE;
    }
#else
    if (fd_ != -1) {
        // Restore original terminal settings
        tcsetattr(fd_, TCSANOW, &oldTermios_);
        ::close(fd_);
        fd_ = -1;
    }
#endif
    
    isOpen_ = false;
}

bool SerialPort::isOpen() const {
    return isOpen_;
}

bool SerialPort::configurePort() {
#ifdef _WIN32
    DCB dcb = {};
    dcb.DCBlength = sizeof(dcb);
    
    if (!GetCommState(handle_, &dcb)) {
        return false;
    }
    
    dcb.BaudRate = static_cast<DWORD>(config_.baudRate);
    dcb.ByteSize = static_cast<BYTE>(config_.dataBits);
    dcb.StopBits = (config_.stopBits == StopBits::STOP_1) ? ONESTOPBIT : TWOSTOPBITS;
    
    switch (config_.parity) {
        case Parity::NONE:
            dcb.Parity = NOPARITY;
            dcb.fParity = FALSE;
            break;
        case Parity::ODD:
            dcb.Parity = ODDPARITY;
            dcb.fParity = TRUE;
            break;
        case Parity::EVEN:
            dcb.Parity = EVENPARITY;
            dcb.fParity = TRUE;
            break;
    }
    
    dcb.fBinary = TRUE;
    dcb.fOutxCtsFlow = FALSE;
    dcb.fOutxDsrFlow = FALSE;
    dcb.fDtrControl = DTR_CONTROL_DISABLE;
    dcb.fDsrSensitivity = FALSE;
    dcb.fRtsControl = RTS_CONTROL_DISABLE;
    dcb.fOutX = FALSE;
    dcb.fInX = FALSE;
    
    if (!SetCommState(handle_, &dcb)) {
        return false;
    }
    
    COMMTIMEOUTS timeouts = {};
    timeouts.ReadIntervalTimeout = 50;
    timeouts.ReadTotalTimeoutConstant = config_.timeoutMs;
    timeouts.ReadTotalTimeoutMultiplier = 10;
    timeouts.WriteTotalTimeoutConstant = config_.timeoutMs;
    timeouts.WriteTotalTimeoutMultiplier = 10;
    
    return SetCommTimeouts(handle_, &timeouts);
#else
    struct termios tty = {};
    
    if (tcgetattr(fd_, &tty) != 0) {
        return false;
    }

    // Set baud rate
    speed_t speed;
    switch (config_.baudRate) {
        case BaudRate::BAUD_9600: speed = B9600; break;
        case BaudRate::BAUD_19200: speed = B19200; break;
        case BaudRate::BAUD_38400: speed = B38400; break;
        case BaudRate::BAUD_57600: speed = B57600; break;
        case BaudRate::BAUD_115200: speed = B115200; break;
        case BaudRate::BAUD_230400: speed = B230400; break;
        case BaudRate::BAUD_460800: speed = B460800; break;
        case BaudRate::BAUD_921600: speed = B921600; break;
        default: speed = B115200; break;
    }

    cfsetospeed(&tty, speed);
    cfsetispeed(&tty, speed);

    // Set data bits
    tty.c_cflag &= ~CSIZE;
    switch (config_.dataBits) {
        case DataBits::DATA_5: tty.c_cflag |= CS5; break;
        case DataBits::DATA_6: tty.c_cflag |= CS6; break;
        case DataBits::DATA_7: tty.c_cflag |= CS7; break;
        case DataBits::DATA_8: tty.c_cflag |= CS8; break;
    }

    // Set stop bits
    if (config_.stopBits == StopBits::STOP_2) {
        tty.c_cflag |= CSTOPB;
    } else {
        tty.c_cflag &= ~CSTOPB;
    }

    // Set parity
    switch (config_.parity) {
        case Parity::NONE:
            tty.c_cflag &= ~PARENB;
            break;
        case Parity::ODD:
            tty.c_cflag |= PARENB;
            tty.c_cflag |= PARODD;
            break;
        case Parity::EVEN:
            tty.c_cflag |= PARENB;
            tty.c_cflag &= ~PARODD;
            break;
    }

    // Other settings
    tty.c_cflag |= CREAD | CLOCAL;
    tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    tty.c_iflag &= ~(IXON | IXOFF | IXANY);
    tty.c_oflag &= ~OPOST;

    // Set timeout
    tty.c_cc[VMIN] = 0;
    tty.c_cc[VTIME] = config_.timeoutMs / 100;
    
    return tcsetattr(fd_, TCSANOW, &tty) == 0;
#endif
}

bool SerialPort::write(const std::vector<uint8_t>& data) {
    if (!isOpen_ || data.empty()) {
        return false;
    }

#ifdef _WIN32
    DWORD bytesWritten = 0;
    return WriteFile(handle_, data.data(), static_cast<DWORD>(data.size()), &bytesWritten, nullptr) &&
           bytesWritten == data.size();
#else
    ssize_t result = ::write(fd_, data.data(), data.size());
    return result == static_cast<ssize_t>(data.size());
#endif
}

bool SerialPort::write(const std::string& data) {
    std::vector<uint8_t> bytes(data.begin(), data.end());
    return write(bytes);
}

std::vector<uint8_t> SerialPort::read(size_t maxBytes) {
    std::vector<uint8_t> buffer;
    if (!isOpen_ || maxBytes == 0) {
        return buffer;
    }

    buffer.resize(maxBytes);

#ifdef _WIN32
    DWORD bytesRead = 0;
    if (ReadFile(handle_, buffer.data(), static_cast<DWORD>(maxBytes), &bytesRead, nullptr)) {
        buffer.resize(bytesRead);
    } else {
        buffer.clear();
    }
#else
    ssize_t bytesRead = ::read(fd_, buffer.data(), maxBytes);
    if (bytesRead > 0) {
        buffer.resize(bytesRead);
    } else {
        buffer.clear();
    }
#endif

    return buffer;
}

std::string SerialPort::readString(size_t maxBytes) {
    auto data = read(maxBytes);
    return std::string(data.begin(), data.end());
}

void SerialPort::flush() {
    if (!isOpen_) return;

#ifdef _WIN32
    FlushFileBuffers(handle_);
#else
    tcflush(fd_, TCIOFLUSH);
#endif
}

size_t SerialPort::available() {
    if (!isOpen_) return 0;

#ifdef _WIN32
    COMSTAT comStat;
    DWORD errors;
    if (ClearCommError(handle_, &errors, &comStat)) {
        return comStat.cbInQue;
    }
    return 0;
#else
    int bytes = 0;
    if (ioctl(fd_, FIONREAD, &bytes) == 0) {
        return bytes;
    }
    return 0;
#endif
}
