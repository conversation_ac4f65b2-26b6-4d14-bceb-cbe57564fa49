#include "SerialPort.h"
#include <iostream>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>
#include <thread>
#include <chrono>
#include <algorithm>
#include <cctype>

class SerialTool {
private:
    SerialPort serialPort;
    bool running = false;

public:
    void run() {
        std::cout << "=== C++ Serial Tool ===" << std::endl;
        std::cout << "Type 'help' for help information" << std::endl << std::endl;

        std::string command;
        while (true) {
            std::cout << "SerialTool> ";
            std::getline(std::cin, command);

            if (command.empty()) continue;

            if (!processCommand(command)) {
                break;
            }
        }
    }

private:
    bool processCommand(const std::string& command) {
        std::vector<std::string> tokens = tokenize(command);
        if (tokens.empty()) return true;

        std::string cmd = toLower(tokens[0]);

        if (cmd == "help" || cmd == "h") {
            showHelp();
        }
        else if (cmd == "list" || cmd == "ls") {
            listPorts();
        }
        else if (cmd == "open") {
            if (tokens.size() < 2) {
                std::cout << "Usage: open <port> [baudrate] [databits] [stopbits] [parity]" << std::endl;
                std::cout << "Example: open COM3 115200 8 1 none" << std::endl;
            } else {
                openPort(tokens);
            }
        }
        else if (cmd == "close") {
            closePort();
        }
        else if (cmd == "status") {
            showStatus();
        }
        else if (cmd == "send" || cmd == "s") {
            if (tokens.size() < 2) {
                std::cout << "Usage: send <data>" << std::endl;
            } else {
                sendData(command.substr(command.find(' ') + 1));
            }
        }
        else if (cmd == "sendhex" || cmd == "sh") {
            if (tokens.size() < 2) {
                std::cout << "Usage: sendhex <hex data>" << std::endl;
                std::cout << "Example: sendhex 48656C6C6F" << std::endl;
            } else {
                sendHexData(command.substr(command.find(' ') + 1));
            }
        }
        else if (cmd == "read" || cmd == "r") {
            readData();
        }
        else if (cmd == "monitor" || cmd == "m") {
            startMonitor();
        }
        else if (cmd == "clear" || cmd == "cls") {
            clearScreen();
        }
        else if (cmd == "exit" || cmd == "quit" || cmd == "q") {
            return false;
        }
        else {
            std::cout << "Unknown command: " << cmd << std::endl;
            std::cout << "Type 'help' for help information" << std::endl;
        }

        return true;
    }

    void showHelp() {
        std::cout << "Available commands:" << std::endl;
        std::cout << "  help, h          - Show help information" << std::endl;
        std::cout << "  list, ls         - List available ports" << std::endl;
        std::cout << "  open <port>      - Open serial port (optional: baudrate databits stopbits parity)" << std::endl;
        std::cout << "  close            - Close serial port" << std::endl;
        std::cout << "  status           - Show port status" << std::endl;
        std::cout << "  send, s <data>   - Send text data" << std::endl;
        std::cout << "  sendhex, sh <hex>- Send hex data" << std::endl;
        std::cout << "  read, r          - Read data" << std::endl;
        std::cout << "  monitor, m       - Start monitor mode (press Enter to exit)" << std::endl;
        std::cout << "  clear, cls       - Clear screen" << std::endl;
        std::cout << "  exit, quit, q    - Exit program" << std::endl;
        std::cout << std::endl;
        std::cout << "Examples:" << std::endl;
        std::cout << "  open COM3 115200 8 1 none" << std::endl;
        std::cout << "  send Hello World" << std::endl;
        std::cout << "  sendhex 48656C6C6F" << std::endl;
    }

    void listPorts() {
        std::cout << "Scanning available ports..." << std::endl;
        auto ports = SerialPort::getAvailablePorts();

        if (ports.empty()) {
            std::cout << "No available ports found" << std::endl;
        } else {
            std::cout << "Available ports:" << std::endl;
            for (const auto& port : ports) {
                std::cout << "  " << port << std::endl;
            }
        }
    }

    void openPort(const std::vector<std::string>& tokens) {
        if (serialPort.isOpen()) {
            std::cout << "Port already open, closing current port" << std::endl;
            serialPort.close();
        }

        std::string portName = tokens[1];
        SerialConfig config;

        // Parse optional parameters
        if (tokens.size() > 2) {
            try {
                int baudRate = std::stoi(tokens[2]);
                config.baudRate = static_cast<BaudRate>(baudRate);
            } catch (...) {
                std::cout << "Invalid baud rate: " << tokens[2] << std::endl;
                return;
            }
        }

        if (tokens.size() > 3) {
            int dataBits = std::stoi(tokens[3]);
            config.dataBits = static_cast<DataBits>(dataBits);
        }

        if (tokens.size() > 4) {
            int stopBits = std::stoi(tokens[4]);
            config.stopBits = static_cast<StopBits>(stopBits);
        }

        if (tokens.size() > 5) {
            std::string parity = toLower(tokens[5]);
            if (parity == "none" || parity == "n") {
                config.parity = Parity::NONE;
            } else if (parity == "odd" || parity == "o") {
                config.parity = Parity::ODD;
            } else if (parity == "even" || parity == "e") {
                config.parity = Parity::EVEN;
            }
        }

        std::cout << "Opening port " << portName << "..." << std::endl;
        if (serialPort.open(portName, config)) {
            std::cout << "Port opened successfully!" << std::endl;
            showStatus();
        } else {
            std::cout << "Failed to open port!" << std::endl;
        }
    }

    void closePort() {
        if (serialPort.isOpen()) {
            serialPort.close();
            std::cout << "Port closed" << std::endl;
        } else {
            std::cout << "Port not open" << std::endl;
        }
    }

    void showStatus() {
        if (!serialPort.isOpen()) {
            std::cout << "Port status: Not open" << std::endl;
            return;
        }

        const auto& config = serialPort.getConfig();
        std::cout << "Port status: Open" << std::endl;
        std::cout << "  Port: " << serialPort.getPortName() << std::endl;
        std::cout << "  Baud rate: " << static_cast<int>(config.baudRate) << std::endl;
        std::cout << "  Data bits: " << static_cast<int>(config.dataBits) << std::endl;
        std::cout << "  Stop bits: " << static_cast<int>(config.stopBits) << std::endl;
        std::cout << "  Parity: ";
        switch (config.parity) {
            case Parity::NONE: std::cout << "None"; break;
            case Parity::ODD: std::cout << "Odd"; break;
            case Parity::EVEN: std::cout << "Even"; break;
        }
        std::cout << std::endl;
        std::cout << "  Timeout: " << config.timeoutMs << "ms" << std::endl;
        std::cout << "  Available bytes: " << serialPort.available() << std::endl;
    }

    void sendData(const std::string& data) {
        if (!serialPort.isOpen()) {
            std::cout << "Port not open" << std::endl;
            return;
        }

        if (serialPort.write(data)) {
            std::cout << "Send successful: \"" << data << "\"" << std::endl;
        } else {
            std::cout << "Send failed" << std::endl;
        }
    }

    void sendHexData(const std::string& hexStr) {
        if (!serialPort.isOpen()) {
            std::cout << "Port not open" << std::endl;
            return;
        }

        std::vector<uint8_t> data = hexStringToBytes(hexStr);
        if (data.empty()) {
            std::cout << "Invalid hex data" << std::endl;
            return;
        }

        if (serialPort.write(data)) {
            std::cout << "Send successful: " << bytesToHexString(data) << std::endl;
        } else {
            std::cout << "Send failed" << std::endl;
        }
    }

    void readData() {
        if (!serialPort.isOpen()) {
            std::cout << "Port not open" << std::endl;
            return;
        }

        auto data = serialPort.read();
        if (data.empty()) {
            std::cout << "No data available" << std::endl;
        } else {
            std::cout << "Received " << data.size() << " bytes:" << std::endl;
            std::cout << "Text: \"" << std::string(data.begin(), data.end()) << "\"" << std::endl;
            std::cout << "Hex: " << bytesToHexString(data) << std::endl;
        }
    }

    void startMonitor() {
        if (!serialPort.isOpen()) {
            std::cout << "Port not open" << std::endl;
            return;
        }

        std::cout << "Starting monitor mode (press Enter to exit)..." << std::endl;
        std::cout << "----------------------------------------" << std::endl;

        running = true;
        while (running) {
            auto data = serialPort.read();
            if (!data.empty()) {
                std::cout << "[" << getCurrentTime() << "] Received: "
                         << std::string(data.begin(), data.end())
                         << " (HEX: " << bytesToHexString(data) << ")" << std::endl;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(10));

            // Simple exit check
            if (std::cin.peek() != EOF) {
                std::string input;
                std::getline(std::cin, input);
                running = false;
            }
        }

        std::cout << "Monitor mode exited" << std::endl;
    }

    void clearScreen() {
#ifdef _WIN32
        system("cls");
#else
        system("clear");
#endif
    }

    // Helper functions
    std::vector<std::string> tokenize(const std::string& str) {
        std::vector<std::string> tokens;
        std::istringstream iss(str);
        std::string token;

        while (iss >> token) {
            tokens.push_back(token);
        }

        return tokens;
    }

    std::string toLower(const std::string& str) {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), ::tolower);
        return result;
    }

    std::vector<uint8_t> hexStringToBytes(const std::string& hex) {
        std::vector<uint8_t> bytes;
        std::string cleanHex;

        // Remove spaces and other non-hex characters
        for (char c : hex) {
            if (std::isxdigit(c)) {
                cleanHex += c;
            }
        }

        if (cleanHex.length() % 2 != 0) {
            return bytes; // Invalid length
        }

        for (size_t i = 0; i < cleanHex.length(); i += 2) {
            try {
                uint8_t byte = static_cast<uint8_t>(std::stoi(cleanHex.substr(i, 2), nullptr, 16));
                bytes.push_back(byte);
            } catch (...) {
                return std::vector<uint8_t>(); // Conversion failed
            }
        }

        return bytes;
    }

    std::string bytesToHexString(const std::vector<uint8_t>& bytes) {
        std::ostringstream oss;
        oss << std::hex << std::uppercase;
        for (size_t i = 0; i < bytes.size(); ++i) {
            if (i > 0) oss << " ";
            oss << std::setw(2) << std::setfill('0') << static_cast<int>(bytes[i]);
        }
        return oss.str();
    }

    std::string getCurrentTime() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        std::ostringstream oss;
#ifdef _WIN32
        struct tm timeinfo;
        localtime_s(&timeinfo, &time_t);
        oss << std::put_time(&timeinfo, "%H:%M:%S");
#else
        oss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
#endif
        oss << "." << std::setfill('0') << std::setw(3) << ms.count();
        return oss.str();
    }
};

int main() {
    try {
        SerialTool tool;
        tool.run();
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
