^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\SERIAL\BUILD\CMAKEFILES\6C665A63E81707D6144BA168FE6C04F2\GENERATE.STAMP.RULE
setlocal
F:\Reference\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Users/<USER>/Documents/augment-projects/serial -BC:/Users/<USER>/Documents/augment-projects/serial/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Documents/augment-projects/serial/build/SerialTool.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
